<!-- Agrimi AI Chat Interface - Figma Design Match -->
<div class="figma-chat-container">
  <!-- Header -->
  <div class="figma-header">
    <div class="figma-header-left">
      <div class="figma-icon-wrapper"></div>
      <div class="figma-title">{{ title }}</div>
    </div>
    <div class="figma-header-controls">
      <button class="figma-control-button">
        <div class="figma-button-icon">⋯</div>
      </button>
      <button class="figma-control-button">
        <div class="figma-button-text">A</div>
      </button>
    </div>
  </div>

  <!-- Main Content Area -->
  <div class="figma-main-content">
    <!-- Chat Message -->
    <div class="figma-chat-message">
      <div class="figma-avatar"></div>
      <div class="figma-message-bubble">
        <p>{{ greeting }}</p>
        <p>{{ helpMessage }}</p>
      </div>
    </div>

    <!-- Input Area -->
    <div class="figma-input-section">
      <!-- Text Input -->
      <div class="figma-input-container">
        <input
          class="figma-input"
          [(ngModel)]="inputValue"
          [placeholder]="inputPlaceholder"
          (keyup.enter)="onSendMessage()"
        />
        <button class="figma-send-button" (click)="onSendMessage()">
          <div class="figma-send-icon">➤</div>
        </button>
      </div>

      <!-- Suggested Questions -->
      <div class="figma-suggestions">
        <button
          *ngFor="let question of suggestedQuestions"
          class="figma-suggestion-pill"
          (click)="onQuestionClick(question)"
        >
          <div class="figma-pill-icon"></div>
          <div class="figma-pill-text">{{ question }}</div>
        </button>
      </div>
    </div>
  </div>
</div>
