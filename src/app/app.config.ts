import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient } from '@angular/common/http';
import { importProvidersFrom } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// ng-zorro-antd imports
import { provideNzI18n, en_US } from 'ng-zorro-antd/i18n';
import { IconDefinition } from '@ant-design/icons-angular';
import { provideNzIcons } from 'ng-zorro-antd/icon';
import { NzConfig, provideNzConfig } from 'ng-zorro-antd/core/config';

// Icon imports for chat interface
import {
  PlusOutline,
  StarOutline,
  SendOutline,
  MessageOutline,
  SettingOutline,
  QuestionCircleOutline,
  PhoneOutline,
  CloseOutline,
  MoreOutline,
  MinusOutline,
} from '@ant-design/icons-angular/icons';

import { routes } from './app.routes';

// Configure icons for preloading
const icons: IconDefinition[] = [
  PlusOutline, // New conversation button
  StarOutline, // Suggested questions
  SendOutline, // Send message button
  MessageOutline, // Conversation history
  SettingOutline, // Settings menu
  QuestionCircleOutline, // Help/Guide menu
  PhoneOutline, // Contact menu
  CloseOutline, // Close buttons
  MoreOutline, // More actions
  MinusOutline, // Minimize button
];

// ng-zorro-antd global configuration
const ngZorroConfig: NzConfig = {
  // Global theme configuration
  theme: {
    primaryColor: '#ab85e8', // Match Figma primary purple
  },
  // Global component defaults
  button: {
    nzSize: 'default',
  },
  icon: {
    nzTheme: 'outline', // Default to outline theme
  },
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(),
    importProvidersFrom(BrowserAnimationsModule),

    // ng-zorro-antd configuration
    provideNzI18n(en_US), // Can be changed to bg_BG for Bulgarian
    provideNzIcons(icons),
    provideNzConfig(ngZorroConfig),
  ],
};
