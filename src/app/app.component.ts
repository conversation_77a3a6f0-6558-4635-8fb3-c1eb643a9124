import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

// ng-zorro-antd imports
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzFlexModule } from 'ng-zorro-antd/flex';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    FormsModule,
    NzLayoutModule,
    NzMenuModule,
    NzButtonModule,
    NzInputModule,
    NzIconModule,
    NzFlexModule,
  ],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss', './figma-styles.scss'],
})
export class AppComponent {
  title = 'Agrimi AI';
  isCollapsed = false;
  inputValue = '';

  // Bulgarian content from Figma
  greeting = 'Здравейте!';
  helpMessage = 'Как мога да Ви помогна днес?';
  inputPlaceholder = 'Попитайте Agrimi AI';

  suggestedQuestions = [
    'Какво означават цветовете на картата?',
    'Как се сменя статус на договор?',
    'Как да филтрирам имоти по собственик или масив?',
  ];

  onCollapsedChange(collapsed: boolean): void {
    this.isCollapsed = collapsed;
  }

  onSendMessage(): void {
    if (this.inputValue.trim()) {
      console.log('Sending message:', this.inputValue);
      this.inputValue = '';
    }
  }

  onQuestionClick(question: string): void {
    this.inputValue = question;
  }
}
