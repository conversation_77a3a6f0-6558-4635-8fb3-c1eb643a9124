// Figma Design Match - Exact Specifications
.figma-chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background: #ffffff;
}

// Header - Exact Figma Match
.figma-header {
  background: #ab85e8;
  display: flex;
  flex-direction: row;
  align-items: start;
  justify-content: space-between;
  padding: 16px;
  border-radius: 16px 16px 0 0;
  width: 100%;
  box-sizing: border-box;
}

.figma-header-left {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: start;
}

.figma-icon-wrapper {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.figma-title {
  font-family: "Montserrat", sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
  text-align: left;
  white-space: nowrap;
}

.figma-header-controls {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: start;
  justify-content: start;
}

.figma-control-button {
  width: 32px;
  height: 32px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 4px 10px;
  border-radius: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.figma-button-icon {
  width: 24px;
  height: 24px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.figma-button-text {
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  white-space: pre;
}

// Main Content Area
.figma-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  width: 100%;
  box-sizing: border-box;
}

// Chat Message
.figma-chat-message {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 32px;
  width: 100%;
  max-width: 600px;
}

.figma-avatar {
  width: 40px;
  height: 40px;
  background: #ab85e8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 16px;
  flex-shrink: 0;
}

.figma-avatar::after {
  content: "AI";
}

.figma-message-bubble {
  background: #f9f0ff;
  padding: 16px;
  border-radius: 12px;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  flex: 1;
}

.figma-message-bubble p {
  margin: 0;
  display: block;
}

.figma-message-bubble p:not(:last-child) {
  margin-bottom: 0;
}

// Input Section
.figma-input-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  max-width: 600px;
}

.figma-input-container {
  background: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 12px 12px 12px 16px;
  border-radius: 12px;
  border: 1px solid #d9d9d9;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  width: 100%;
  box-sizing: border-box;
}

.figma-input {
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  border: none;
  outline: none;
  background: transparent;
  flex: 1;
}

.figma-input::placeholder {
  color: rgba(0, 0, 0, 0.25);
}

.figma-send-button {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.figma-send-icon {
  width: 24px;
  height: 24px;
  color: #ab85e8;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Suggested Questions
.figma-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: start;
  justify-content: start;
  width: 100%;
}

.figma-suggestion-pill {
  background: #f9f0ff;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  text-align: left;
  white-space: nowrap;
}

.figma-pill-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0);
  flex-shrink: 0;
}

.figma-pill-icon::after {
  content: "⭐";
  display: block;
}

.figma-pill-text {
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
  text-align: left;
  white-space: pre;
}

// Responsive Design
@media (max-width: 768px) {
  .figma-main-content {
    padding: 16px;
  }

  .figma-chat-message {
    margin-bottom: 24px;
  }

  .figma-input-section {
    gap: 16px;
  }

  .figma-suggestions {
    flex-direction: column;
    align-items: stretch;
  }

  .figma-suggestion-pill {
    width: 100%;
    justify-content: flex-start;
    white-space: normal;
    text-align: left;
  }

  .figma-header {
    padding: 12px 16px;
  }

  .figma-title {
    font-size: 14px;
  }

  .figma-header-controls {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .figma-main-content {
    padding: 12px;
  }

  .figma-input-container {
    padding: 10px 10px 10px 14px;
  }

  .figma-message-bubble {
    padding: 12px;
  }

  .figma-suggestion-pill {
    padding: 10px 14px;
    font-size: 13px;
  }
}
