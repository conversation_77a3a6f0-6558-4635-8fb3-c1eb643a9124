/* Global styles for Agrimi AI Chat Interface */

// Import ng-zorro-antd styles
@import "ng-zorro-antd/ng-zorro-antd.min.css";

// Import Montserrat font from Google Fonts
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@500;700&display=swap");

// CSS Variables for Design System
:root {
  --primary-purple: #ab85e8;
  --light-purple: #f9f0ff;
  --white: #ffffff;
  --text-primary: rgba(0, 0, 0, 0.85);
  --text-secondary: rgba(0, 0, 0, 0.45);
  --text-disabled: rgba(0, 0, 0, 0.25);
  --border-color: #d9d9d9;
  --shadow-light: 0px 2px 8px rgba(0, 0, 0, 0.15);
}

// Global Typography Classes
.montserrat-bold-16 {
  font-family: "Montserrat", sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
}

.montserrat-medium-14 {
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-disabled {
  color: var(--text-disabled);
}

.text-white {
  color: var(--white);
}

// Global Reset and Base Styles
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: "Montserrat", sans-serif;
  background-color: var(--white);
  color: var(--text-primary);
}

// Header Styling
.app-header {
  background-color: var(--primary-purple) !important;
  color: var(--white) !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px !important;
  border-radius: 16px 16px 0 0;
  font-family: "Montserrat", sans-serif;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  height: auto !important;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.header-control-button {
  width: 32px !important;
  height: 32px !important;
  border-radius: 8px !important;
  padding: 4px 10px !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: var(--white) !important;
}

.header-control-button:hover {
  background-color: rgba(255, 255, 255, 0.3) !important;
}

// Chat Message Bubble
.chat-message-bubble {
  background-color: var(--light-purple);
  padding: 16px;
  border-radius: 12px;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: var(--text-primary);
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-purple);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 700;
}

// Suggested Question Pills
.question-pill {
  background-color: var(--light-purple) !important;
  border-radius: 8px !important;
  border: none !important;
  padding: 12px 16px !important;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: var(--text-primary) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  height: auto !important;
  white-space: normal !important;
  text-align: left !important;
}

.question-pill:hover {
  background-color: #f0e6ff !important;
}

.question-pills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.question-pill-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

// Input Area Styling
.input-area {
  background: var(--white);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.input-field {
  background: var(--white) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  padding: 12px 12px 12px 16px !important;
  box-shadow: var(--shadow-light) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.input-field .ant-input {
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 22px !important;
}

.input-field .ant-input::placeholder {
  color: var(--text-disabled) !important;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 22px !important;
}

.send-button {
  width: 32px !important;
  height: 32px !important;
  background-color: var(--primary-purple) !important;
  border: none !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: var(--white) !important;
}

.send-button:hover {
  background-color: #9b75d8 !important;
}

// Main Layout Styling
.main-layout {
  height: 100vh;
  background-color: var(--white);
}

.content-wrapper {
  height: calc(100vh - 80px); // Adjust based on header height
}

.sidebar {
  background-color: var(--white) !important;
  border-right: 1px solid var(--border-color) !important;
}

.main-content {
  background-color: var(--white) !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
}

// Sidebar Styling
.sidebar-content {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.new-conversation-btn {
  margin-bottom: 24px !important;
  border-radius: 8px !important;
  font-family: "Montserrat", sans-serif !important;
  font-weight: 500 !important;
}

.conversation-history {
  flex: 1;
  overflow-y: auto;
}

.bottom-navigation {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

// Chat Content Styling
.chat-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.welcome-section {
  max-width: 600px;
  margin-bottom: 32px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.welcome-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 32px;
}

// Responsive Design
@media (max-width: 768px) {
  .header-controls {
    gap: 8px;
  }

  .question-pills-container {
    flex-direction: column;
  }

  .question-pill {
    width: 100% !important;
    justify-content: flex-start !important;
  }

  .input-area {
    padding: 16px !important;
  }

  .chat-content {
    padding: 16px !important;
  }
}
